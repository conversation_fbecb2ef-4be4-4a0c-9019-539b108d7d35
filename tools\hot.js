const fs = require('fs');
const path = require('path');

function convertToHotReloadCompatible(filePath) {
    if (!fs.existsSync(filePath)) {
        console.error(`❌ File not found: ${filePath}`);
        process.exit(1);
    }

    console.log(`🔄 Converting ${filePath} to hot reload compatible...`);

    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;

    // Detect existing line ending style
    const hasWindowsLineEndings = content.includes('\r\n');
    const lineEnding = hasWindowsLineEndings ? '\r\n' : '\n';
    
    console.log(`📝 Detected line endings: ${hasWindowsLineEndings ? 'Windows (CRLF)' : 'Unix (LF)'}`);

    // Normalize to Unix line endings for processing
    content = content.replace(/\r\n/g, '\n');
    
    // Extract plugin name
    let pluginNameMatch = content.match(/const\s+pluginName\s*=\s*["']([^"']+)["']/);
    let pluginName = pluginNameMatch ? pluginNameMatch[1] : 'Unknown Plugin';

    // If no pluginName found, try to detect from filename
    if (!pluginNameMatch) {
        const fileName = path.basename(filePath, '.js');
        if (fileName.toLowerCase().includes('spectrum')) {
            pluginName = 'Spectrum Graph';
        } else if (fileName.toLowerCase().includes('scanner')) {
            pluginName = 'Scanner';
        } else {
            pluginName = fileName.replace(/[_-]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }
    }

    // Generate unique state variable name based on file path to avoid conflicts
    const customHashArg = process.argv[3];
    let customHash = null;
    process.argv.slice(2).forEach(arg => {
      if (arg.startsWith('--hash=')) {
        customHash = arg.split('=')[1];
      }
    });
    const fileHash = customHash ? customHash : require('crypto').createHash('md5').update(filePath).digest('hex').substring(0, 8);

    const baseStateName = pluginName.replace(/\s+/g, '');
    const stateVarName = `${baseStateName}State_${fileHash}`;
    
    console.log(`📝 Plugin: ${pluginName}`);
    console.log(`🔧 State variable: ${stateVarName}`);

    // Step 1: Add state management
    const stateManagement = `
// Hot reload state management
if (!global.${stateVarName}) {
    global.${stateVarName} = {
        timers: new Set(),
        sockets: new Set(),
        originalHandleData: null,
        routeAdded: false,
        isInitialized: false,
        textSocket: null,
        extraSocket: null,
        registeredRoutes: new Set(),
        routerInstances: new Set(),
        pluginVariables: {},
        routeHandlers: new Map()
    };
}
`;
    
    // Add state management at the very beginning after 'use strict'
    const useStrictPattern = /('use strict';?\s*\n)/;
    if (content.match(useStrictPattern)) {
        content = content.replace(useStrictPattern, `$1${stateManagement}\n`);
    } else {
        // Add at the very beginning after initial comments
        const afterCommentsPattern = /(^(?:\/\*[\s\S]*?\*\/\s*\n)*(?:\/\/.*\n)*)/;
        content = content.replace(afterCommentsPattern, `$1${stateManagement}\n`);
    }

    // Step 2: Check existing const variables and usage in original content
    const hasDebug = originalContent.includes('const debug');
    const hasWebserverPort = originalContent.includes('const webserverPort');
    const hasExternalWsUrl = originalContent.includes('const externalWsUrl');

    console.log(`🔍 Existing const variables - debug: ${hasDebug}, webserverPort: ${hasWebserverPort}, externalWsUrl: ${hasExternalWsUrl}`);

    // Step 3: Check if the plugin actually uses these variables before adding them
    // Use more robust pattern matching to catch different coding styles
    const usesWebserverPort = (
        /\bwebserverPort\b/.test(originalContent) &&
        !hasWebserverPort
    );
    const usesExternalWsUrl = (
        /\bexternalWsUrl\b/.test(originalContent) &&
        !hasExternalWsUrl
    );
    const usesDebug = (
        /\bdebug\b/.test(originalContent) &&
        !hasDebug &&
        // Avoid false positives from comments or console.debug
        !/\/\/.*\bdebug\b/.test(originalContent) &&
        !/console\.debug/.test(originalContent)
    );

    console.log(`🔍 Variable usage - debug: ${usesDebug}, webserverPort: ${usesWebserverPort}, externalWsUrl: ${usesExternalWsUrl}`);

    // Additional safety check: warn about potential config usage without proper imports
    if (/\bconfig\b/.test(originalContent) && !/require.*server_config/.test(originalContent)) {
        console.log(`⚠️  Warning: Plugin appears to use 'config' but doesn't import server_config`);
        console.log(`   This may cause "config is not defined" errors`);
        console.log(`   Consider importing: const { serverConfig } = require('../../server/server_config');`);
    }

    // Step 4: Add missing const variables only if needed
    let constVariablesSection = '';
    if (usesDebug || usesWebserverPort || usesExternalWsUrl) {
        constVariablesSection += '\n// const variables\n';
        if (usesDebug) constVariablesSection += 'const debug = false;\n';
        if (usesWebserverPort) {
            // Import serverConfig if needed for webserverPort
            // Check for different ways config might already be imported
            const hasServerConfig = /\bserverConfig\b/.test(content);
            const hasConfigImport = /require.*server_config/.test(content);

            if (!hasServerConfig && !hasConfigImport) {
                constVariablesSection += 'const { serverConfig } = require(\'../../server/server_config\');\n';
            }
            constVariablesSection += 'const webserverPort = serverConfig.webserver.webserverPort || 8080;\n';
        }
        if (usesExternalWsUrl) {
            // Ensure webserverPort is available for externalWsUrl
            if (!usesWebserverPort && !hasWebserverPort) {
                const hasServerConfig = /\bserverConfig\b/.test(content);
                const hasConfigImport = /require.*server_config/.test(content);

                if (!hasServerConfig && !hasConfigImport) {
                    constVariablesSection += 'const { serverConfig } = require(\'../../server/server_config\');\n';
                }
                constVariablesSection += 'const webserverPort = serverConfig.webserver.webserverPort || 8080;\n';
            }
            constVariablesSection += 'const externalWsUrl = `ws://127.0.0.1:${webserverPort}`;\n';
        }
        constVariablesSection += '\n';
    }

    // Step 5: Detect routers for later wrapping
    const routerImportPattern = /const\s+(\w*[Rr]outer\w*)\s*=\s*require\(['"][^'"]*endpoints['"]\)/g;
    let routerMatch;
    const detectedRouters = [];

    while ((routerMatch = routerImportPattern.exec(originalContent)) !== null) {
        detectedRouters.push(routerMatch[1]);
    }

    console.log(`🔍 Detected routers: ${detectedRouters.join(', ')}`);

    // Step 6: Add helper functions (always needed for hot reload)
    let helperSection = constVariablesSection;
    
    helperSection += `// Tracked timer functions for hot reload
const originalSetTimeout = setTimeout;
const originalSetInterval = setInterval;
const originalClearTimeout = clearTimeout;
const originalClearInterval = clearInterval;

function trackedSetTimeout(callback, delay, ...args) {
    const timerId = originalSetTimeout((...callbackArgs) => {
        global.${stateVarName}.timers.delete(timerId);
        callback(...callbackArgs);
    }, delay, ...args);
    global.${stateVarName}.timers.add(timerId);
    return timerId;
}

function trackedSetInterval(callback, delay, ...args) {
    const timerId = originalSetInterval(callback, delay, ...args);
    global.${stateVarName}.timers.add(timerId);
    return timerId;
}

function trackedClearTimeout(timerId) {
    global.${stateVarName}.timers.delete(timerId);
    return originalClearTimeout(timerId);
}

function trackedClearInterval(timerId) {
    global.${stateVarName}.timers.delete(timerId);
    return originalClearInterval(timerId);
}

// Route registration for hot reload - handles variable scope issues
function safeRouterGet(router, path, ...handlers) {
    const routeKey = \`GET:\${path}\`;

    // Store the current handler for potential cleanup
    if (global.${stateVarName}.routeHandlers.has(routeKey)) {
        logInfo(\`Plugin: Updating existing route \${routeKey}\`);
    } else {
        logInfo(\`Plugin: Registering new route \${routeKey}\`);
        global.${stateVarName}.registeredRoutes.add(routeKey);
    }

    // Store the handler reference for cleanup
    global.${stateVarName}.routeHandlers.set(routeKey, handlers);

    return router.get(path, ...handlers);
}

function safeRouterPost(router, path, ...handlers) {
    const routeKey = \`POST:\${path}\`;

    // Store the current handler for potential cleanup
    if (global.${stateVarName}.routeHandlers.has(routeKey)) {
        logInfo(\`Plugin: Updating existing route \${routeKey}\`);
    } else {
        logInfo(\`Plugin: Registering new route \${routeKey}\`);
        global.${stateVarName}.registeredRoutes.add(routeKey);
    }

    // Store the handler reference for cleanup
    global.${stateVarName}.routeHandlers.set(routeKey, handlers);

    return router.post(path, ...handlers);
}


`;

    // Insert helper functions after state management
    content = content.replace(stateManagement, `${stateManagement}${helperSection}`);

    // Step 3.5: Convert router method calls to safe versions
    detectedRouters.forEach(routerName => {
        // Convert router.get() calls to safeRouterGet()
        content = content.replace(
            new RegExp(`\\b${routerName}\\.get\\s*\\(`, 'g'),
            `safeRouterGet(${routerName}, `
        );

        // Convert router.post() calls to safeRouterPost()
        content = content.replace(
            new RegExp(`\\b${routerName}\\.post\\s*\\(`, 'g'),
            `safeRouterPost(${routerName}, `
        );
    });

    // Step 3.6: Fix variable scope issues for hot reload compatibility
    // Apply fixes in the correct order and be very specific to avoid breaking declarations

    // First, fix the route handler completely by finding the exact pattern
    const routeHandlerPattern = /safeRouterGet\(endpointsRouter,\s*'\/spectrum-graph-plugin',\s*\(req,\s*res\)\s*=>\s*\{[\s\S]*?\}\);/g;
    content = content.replace(routeHandlerPattern, (routeHandler) => {
        let fixed = routeHandler;

        // Fix ipAddress assignment in route handler
        fixed = fixed.replace(
            /(\s+)ipAddress\s*=\s*([^;]+);/g,
            `$1// Update both global state and local variable for hot reload compatibility\n$1global.${stateVarName}.pluginVariables.ipAddress = $2;\n$1ipAddress = global.${stateVarName}.pluginVariables.ipAddress;`
        );

        // Fix ipTimeout assignment in route handler
        fixed = fixed.replace(
            /(\s+)ipTimeout\s*=\s*(trackedSetTimeout\([^}]+\}\s*,\s*\d+\));/g,
            `$1global.${stateVarName}.pluginVariables.ipTimeout = $2;\n$1ipTimeout = global.${stateVarName}.pluginVariables.ipTimeout;`
        );

        // Fix res.json call
        fixed = fixed.replace(
            /(\s+)res\.json\(spectrumData\);/g,
            `$1// Use global state for spectrumData to ensure fresh data after hot reload\n$1res.json(global.${stateVarName}.pluginVariables.spectrumData || spectrumData);`
        );

        return fixed;
    });

    // Fix updateSpectrumData function
    content = content.replace(
        /(function updateSpectrumData\([^)]*\)\s*\{[\s\S]*?spectrumData\s*=\s*[^;]+;)(\s*\})/g,
        `$1\n    // Update global state for hot reload compatibility\n    global.${stateVarName}.pluginVariables.spectrumData = spectrumData;$2`
    );

    // Fix logging statements with ipAddress - target specific patterns
    content = content.replace(
        /(\s+)if\s*\(isFirstRun\s*&&\s*ipAddress\)\s*logInfo\(([^)]*\b)ipAddress(\b[^)]*)\);/g,
        `$1// Use global state for ipAddress to ensure fresh value after hot reload\n$1const currentIpAddress = global.${stateVarName}.pluginVariables.ipAddress || ipAddress;\n$1if (isFirstRun && currentIpAddress) logInfo($2currentIpAddress$3);`
    );

    content = content.replace(
        /(\s+)if\s*\([^)]*ipAddress\.includes[^)]*\)\s*logInfo\(([^)]*\b)ipAddress(\b[^)]*)\);/g,
        `$1// Use global state for ipAddress to ensure fresh value after hot reload\n$1const currentIpAddress = global.${stateVarName}.pluginVariables.ipAddress || ipAddress;\n$1if (logLocalCommands || (!logLocalCommands && (!currentIpAddress.includes('ws://')) || isFirstRun)) logInfo($2currentIpAddress$3);`
    );

    console.log(`🔧 Fixed variable scope issues with route handler pattern matching`);

    // Step 4: Detect WebSocket variables
    const webSocketVars = new Set();
    const webSocketPattern = /(?:(const|let|var)\s+)?(\w+)\s*=\s*new\s+WebSocket\s*\(/g;
    let match;
    while ((match = webSocketPattern.exec(content)) !== null) {
        webSocketVars.add(match[2]);
    }
    
    console.log(`🔍 Detected WebSocket variables: ${Array.from(webSocketVars).join(', ')}`);

    // Step 5: Convert timer calls
    content = content.replace(/setTimeout\s*\(/g, 'trackedSetTimeout(');
    content = content.replace(/setInterval\s*\(/g, 'trackedSetInterval(');
    content = content.replace(/clearTimeout\s*\(/g, 'trackedClearTimeout(');
    content = content.replace(/clearInterval\s*\(/g, 'trackedClearInterval(');



    // Step 6: Handle WebSocket imports and add WebSocket functions
    const webSocketImportPattern = /(const WebSocket = require\(['"]ws['"]\);)/;
    const webSocketImportMatch = content.match(webSocketImportPattern);
    
    console.log(`🔍 WebSocket import found: ${!!webSocketImportMatch}`);
    
    if (webSocketImportMatch) {
        const webSocketSection = `
const originalWebSocket = WebSocket;

function trackedWebSocket(url, protocols) {
    const socket = new originalWebSocket(url, protocols);
    global.${stateVarName}.sockets.add(socket);
    logInfo(\`Plugin: Created WebSocket to \${url}, total: \${global.${stateVarName}.sockets.size}\`);

    socket.addEventListener('close', () => {
        global.${stateVarName}.sockets.delete(socket);
        logInfo(\`Plugin: WebSocket closed, remaining: \${global.${stateVarName}.sockets.size}\`);
    });

    return socket;
}
`;
        content = content.replace(webSocketImportPattern, `$1${webSocketSection}`);
    }

    // Step 7: Convert WebSocket assignments - use simple string replacement to prevent double replacements
    webSocketVars.forEach(varName => {
        // Handle WebSocket creations - replace with trackedWebSocket
        content = content.replace(
            new RegExp(`const\\s+${varName}\\s*=\\s*new\\s+WebSocket\\s*\\(`, 'g'),
            `global.${stateVarName}.${varName} = trackedWebSocket(`
        );

        content = content.replace(
            new RegExp(`(let|var)\\s+${varName}\\s*=\\s*new\\s+WebSocket\\s*\\(`, 'g'),
            `global.${stateVarName}.${varName} = trackedWebSocket(`
        );

        // Handle simple assignments (but not if already converted)
        content = content.replace(
            new RegExp(`\\b${varName}\\s*=\\s*new\\s+WebSocket\\s*\\(`, 'g'),
            (match, offset) => {
                // Check if this line already has global state
                const lineStart = content.lastIndexOf('\n', offset) + 1;
                const lineEnd = content.indexOf('\n', offset);
                const line = content.substring(lineStart, lineEnd === -1 ? content.length : lineEnd);

                if (line.includes(`global.${stateVarName}.${varName}`)) {
                    return match; // Don't replace if already converted
                }
                return `global.${stateVarName}.${varName} = trackedWebSocket(`;
            }
        );
    });

    // Second pass: convert variable references
    webSocketVars.forEach(varName => {
        // Convert variable checks in conditions
        content = content.replace(
            new RegExp(`\\b${varName}(?=\\s*\\|\\||\\s*&&|\\s*\\?|\\s*===|\\s*!==|\\s*==|\\s*!=)`, 'g'),
            (match, offset) => {
                const lineStart = content.lastIndexOf('\n', offset) + 1;
                const lineEnd = content.indexOf('\n', offset);
                const line = content.substring(lineStart, lineEnd === -1 ? content.length : lineEnd);

                if (line.includes(`global.${stateVarName}.${varName}`)) {
                    return match;
                }
                return `global.${stateVarName}.${varName}`;
            }
        );

        // Convert readyState checks
        content = content.replace(
            new RegExp(`\\b${varName}\\.readyState`, 'g'),
            (match, offset) => {
                const lineStart = content.lastIndexOf('\n', offset) + 1;
                const lineEnd = content.indexOf('\n', offset);
                const line = content.substring(lineStart, lineEnd === -1 ? content.length : lineEnd);

                if (line.includes(`global.${stateVarName}.${varName}.readyState`)) {
                    return match;
                }
                return `global.${stateVarName}.${varName}.readyState`;
            }
        );

        // Convert method calls
        content = content.replace(
            new RegExp(`\\b${varName}\\.(send|close|addEventListener|removeEventListener)\\(`, 'g'),
            (match, method, offset) => {
                const lineStart = content.lastIndexOf('\n', offset) + 1;
                const lineEnd = content.indexOf('\n', offset);
                const line = content.substring(lineStart, lineEnd === -1 ? content.length : lineEnd);

                if (line.includes(`global.${stateVarName}.${varName}.${method}`)) {
                    return match;
                }
                return `global.${stateVarName}.${varName}.${method}(`;
            }
        );
    });

    // Step 8: Convert WebSocket creation calls
    content = content.replace(/new\s+WebSocket\s*\(/g, 'trackedWebSocket(');
    
    // Add local variable assignments after global WebSocket assignments
    webSocketVars.forEach(varName => {
        const globalAssignPattern = new RegExp(`(global\\.${stateVarName}\\.${varName}\\s*=\\s*trackedWebSocket\\([^)]+\\);)`, 'g');

        // Check if variable is already declared (let/var/const) - look for declarations like "let varName," or "let varName;"
        const declarationPatterns = [
            new RegExp(`\\blet\\s+[^;]*\\b${varName}\\b[^;]*[;,]`, 'g'),
            new RegExp(`\\bvar\\s+[^;]*\\b${varName}\\b[^;]*[;,]`, 'g'),
            new RegExp(`\\bconst\\s+[^;]*\\b${varName}\\b[^;]*[;,]`, 'g')
        ];

        const isAlreadyDeclared = declarationPatterns.some(pattern => content.match(pattern));

        if (isAlreadyDeclared) {
            // Use simple assignment for already declared variables
            content = content.replace(globalAssignPattern, `$1\n            ${varName} = global.${stateVarName}.${varName};`);
        } else {
            // Use const declaration for variables that were declared inline
            content = content.replace(globalAssignPattern, `$1\nconst ${varName} = global.${stateVarName}.${varName};`);
        }
    });

    // Step 9: Add cleanup function
    const webSocketCleanupLines = Array.from(webSocketVars).map(varName => 
        `    global.${stateVarName}.${varName} = null;`
    ).join('\n');
    
    const cleanupFunction = `
// Cleanup function for hot reload
function cleanup() {
    logInfo('Plugin: Cleaning up for hot reload...');

    // Clear all timers (Cleanup for long-running timers)
    logInfo(\`Plugin: Clearing \${global.${stateVarName}.timers.size} tracked timers...\`);
    global.${stateVarName}.timers.forEach(timer => {
        clearTimeout(timer);
        clearInterval(timer); // Also try clearInterval for safety
    });
    global.${stateVarName}.timers.clear();

    // Handle WebSocket cleanup based on plugin type (detect from state variable name)
    if ('${stateVarName}'.toLowerCase().includes('scanner')) {
        // For scanner plugins: preserve WebSocket connections to prevent timer-related issues
        logInfo(\`Plugin: Preserving \${global.${stateVarName}.sockets.size} active WebSocket connections for hot reload compatibility...\`);
        logInfo('Plugin: WebSocket variables preserved for hot reload compatibility');
    } else {
        // For other plugins: close WebSocket connections normally
        logInfo(\`Plugin: Closing \${global.${stateVarName}.sockets.size} WebSocket connections...\`);
        global.${stateVarName}.sockets.forEach(socket => {
            if (socket && socket.readyState === 1) {
                socket.close(1000, 'Plugin reloading');
            }
        });
        global.${stateVarName}.sockets.clear();

        // Clear WebSocket variables
${webSocketCleanupLines}
        logInfo('Plugin: WebSocket cleanup completed');
    }

    // Clear registered routes for next load
    logInfo(\`Plugin: Clearing \${global.${stateVarName}.registeredRoutes.size} registered routes...\`);
    global.${stateVarName}.registeredRoutes.clear();

    // Restore original dataHandler if it exists
    if (global.${stateVarName}.originalHandleData) {
        datahandlerReceived.handleData = global.${stateVarName}.originalHandleData;
        logInfo('Plugin: Restored original dataHandler');
    }



    global.${stateVarName}.isInitialized = false;
    logInfo('Plugin: Cleanup completed');
}

// Register cleanup with hot reload system
if (typeof __plugin !== 'undefined' && __plugin.registerCleanup) {
    __plugin.registerCleanup(cleanup);
}

// Initialisation with variable management
if (global.${stateVarName}.isInitialized) {
    logInfo('Plugin: Already initialized, cleaning up previous instance...');
    cleanup();
}

global.${stateVarName}.isInitialized = true;

// Store critical plugin variables in global state for route handlers
if (typeof ipAddress !== 'undefined') global.${stateVarName}.pluginVariables.ipAddress = ipAddress;
if (typeof ipTimeout !== 'undefined') global.${stateVarName}.pluginVariables.ipTimeout = ipTimeout;
if (typeof spectrumData !== 'undefined') global.${stateVarName}.pluginVariables.spectrumData = spectrumData;
if (typeof textSocket !== 'undefined') global.${stateVarName}.pluginVariables.textSocket = textSocket;
`;

    // Add cleanup function at the end of the file
    content += cleanupFunction;

    // Step 10: Convert WebSocket constants
    content = content.replace(/WebSocket\.OPEN/g, '1');
    content = content.replace(/WebSocket\.CLOSED/g, '3');
    content = content.replace(/WebSocket\.CONNECTING/g, '0');
    content = content.replace(/WebSocket\.CLOSING/g, '2');

    // Restore original line endings
    if (lineEnding === '\r\n') {
        content = content.replace(/\n/g, '\r\n');
    }

    // Generate hot reload filename
    const hotReloadPath = filePath.replace('_server.js', '_server_hot.js');

    // Validate that we're working with a server file
    if (!filePath.endsWith('_server.js')) {
        console.error('❌ Error: File must end with "_server.js" to convert to hot reload version');
        process.exit(1);
    }

    if (hotReloadPath === filePath) {
        console.error('❌ Error: Could not generate hot reload filename');
        process.exit(1);
    }

    // Save converted content to hot reload file, leave original untouched
    fs.writeFileSync(hotReloadPath, content);

    // Count conversions
    const timerMatches = (originalContent.match(/(setTimeout|setInterval)/g) || []).length;
    const clearTimerMatches = (originalContent.match(/(clearTimeout|clearInterval)/g) || []).length;
    const socketMatches = (originalContent.match(/new\s+WebSocket/g) || []).length;
    const routerMatches = detectedRouters.length;

    console.log('✅ Conversion completed!');
    console.log(`📁 Original file: ${filePath} (unchanged)`);
    console.log(`🔥 Hot reload version created: ${hotReloadPath}`);
    console.log('');
    console.log('📊 Conversion Summary:');
    console.log(`   • ${timerMatches} timer calls converted`);
    console.log(`   • ${clearTimerMatches} timer clear calls converted`);
    console.log(`   • ${socketMatches} WebSocket creations converted`);
    console.log(`   • ${routerMatches} routers wrapped for duplicate prevention`);
    console.log('   • Added global state management');
    console.log('   • Added cleanup function');
    console.log('   • Added initialization guards');
    console.log('   • Added router duplicate prevention');
    console.log('   • Added timer cleanup');

    // Show what const variables were added (if any)
    const addedVars = [];
    if (usesDebug) addedVars.push('debug');
    if (usesWebserverPort) addedVars.push('webserverPort');
    if (usesExternalWsUrl) addedVars.push('externalWsUrl');

    if (addedVars.length > 0) {
        console.log(`   • Added const variables: ${addedVars.join(', ')}`);
    } else {
        console.log(`   • No additional const variables needed`);
    }
    console.log('');
    console.log('🎯 Hot Reload Priority System:');
    console.log('   • Server will now prioritize *_server_hot.js over *_server.js');
    console.log('   • Original plugin remains as fallback if hot reload version is removed');
    console.log('   • Both versions can coexist safely');
    console.log('   • Router registration conflicts prevented');
    console.log('   • Long-running timers properly tracked and cleared');
    console.log('');
    console.log('⚠️  Note: Conversion with router and timer improvements');
    console.log('   Manual verification recommended for plugin-specific patterns');
}

// CLI usage
if (require.main === module) {
    const filePath = process.argv[2];
    if (!filePath) {
        console.log(`Usage: node ${path.basename(__filename)} <plugin-server-file>`);
        process.exit(1);
    }
    
    convertToHotReloadCompatible(filePath);
}

module.exports = { convertToHotReloadCompatible };